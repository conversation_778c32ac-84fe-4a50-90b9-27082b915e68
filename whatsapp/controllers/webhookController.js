const whatsappService = require('../services/whatsappService');
const sessionService = require('../services/sessionService');
const restaurantStore = require('../services/restaurantStore');
const { fetchCustomerInfo, fetchBrandInfo } = require('../machines/orderFsmActions');
const logger = require('../../helpers/logger');
const axios = require('axios');

class WebhookController {
  /**
   * Handle incoming webhook requests
   * Process all types of webhook events
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async handleIncoming(req, res) {
    try {
      logger.debug('WEBHOOK_RAW_DATA', {
        source: 'whatsapp',
        eventType: req.body?.data?.attributes?.type,
        // headers: req.headers,
        body: req.body,
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      res.status(202).send('Accepted');
      const { body } = req;
      if (body && body.data) {
        const eventType = body.data.attributes?.type;
        logger.debug(`Processing webhook event type: ${eventType}`);
        switch (eventType) {
          case 'messages.received':
            await this.processIncomingMessage(body);
            break;
          case 'messages.status_update':
            await this.processMessageStatusUpdate(body);
            break;
          case 'dialogues.status_update':
            await this.processDialogueStatusUpdate(body);
            break;
          default:
            logger.warn(`Unhandled webhook event type: ${eventType}`);
        }
      } else {
        logger.warn('Invalid webhook payload structure', { body });
      }
    } catch (error) {
      logger.error('Webhook handling error:', {
        error: error.message,
        stack: error.stack?.split('\n').slice(0, 3).join('\n')
      });
    }
  }

  /**
   * Process incoming message from webhook
   * @param {Object} webhookData - Webhook payload
   */
  async processIncomingMessage(webhookData) {
    try {
      // Step 1: Extract essential data from webhook payload
      const { message, dialogue, messageId, dialogueId, recipientId, brandWhatsappId } = this.extractWebhookData(
        webhookData
      );
      if (!message) {
        logger.error('Missing message data');
        return;
      }
      if (!dialogueId) {
        logger.error('Missing dialogue ID');
        return;
      }
      if (!recipientId) {
        logger.error('Missing recipient ID');
        return;
      }
      logger.debug('Processing incoming message:', { messageId, dialogueId, recipientId, brandWhatsappId });

      // Step 2: Extract message content and metadata first
      const { messageContent, externalID, messageType } = this.extractMessageContent(message);
      logger.debug('Extracted message content:', {
        messageId,
        messageType,
        messageContent: messageContent.substring(0, 100),
        externalID
      });

      // Step 3: Check for expired order requests
      const existingSession = await sessionService.getSession(dialogueId);
      const isOrderOperation = externalID && ['confirm_order', 'cancel_order'].includes(externalID);

      if (!existingSession && isOrderOperation) {
        logger.warn('Expired request detected', {
          dialogueId,
          externalID,
          messageContent: messageContent.substring(0, 50)
        });

        // Create new session for expired request
        const session = await this.getOrCreateSession(dialogueId, recipientId, brandWhatsappId);
        if (!session) {
          logger.error('Failed to create session for expired request', { dialogueId, recipientId });
          return;
        }

        // Queue expired request event for processing
        await sessionService.queueSessionEvent(dialogueId, {
          type: 'EXPIRED_REQUEST',
          data: {
            messageId,
            messageType,
            messageContent,
            externalID,
            session,
            requestType: 'ORDER_OPERATION'
          }
        });

        logger.debug('Expired request queued for processing', { dialogueId, externalID });
        return;
      }

      // Step 4: Get or create session for normal processing
      const session = existingSession || await this.getOrCreateSession(dialogueId, recipientId, brandWhatsappId);
      if (!session) {
        logger.error('Failed to get or create session', { dialogueId, recipientId });
        return;
      }
      if (!session.dialogueId) {
        logger.error('Session missing dialogueId', { dialogueId, recipientId });
        return;
      }

      // Step 5: Queue the message for processing
      await sessionService.queueSessionEvent(dialogueId, {
        type: 'MESSAGE_RECEIVED',
        data: {
          messageId,
          messageType,
          messageContent,
          externalID,
          session
        }
      });

      logger.debug(`Message queued for processing: ${dialogueId}`);
    } catch (error) {
      logger.error('Error processing incoming message:', {
        error: error.message,
        stack: error.stack?.split('\n').slice(0, 3).join('\n')
      });
    }
  }

  /**
   * Extract essential data from webhook payload
   * @param {Object} webhookData - The webhook data
   * @returns {Object} - Extracted message, dialogue, and IDs
   */
  extractWebhookData(webhookData) {
    // Extract message and dialogue information from included array
    const message = webhookData.included?.find(item => item.type === 'messages');
    const dialogue = webhookData.included?.find(item => item.type === 'dialogues');

    // 验证基本数据结构
    if (!webhookData.included || !Array.isArray(webhookData.included)) {
      logger.error('Invalid webhook data structure', { webhookData: JSON.stringify(webhookData).substring(0, 500) });
      return {};
    }

    // 验证消息对象
    if (!message || !message.id) {
      logger.error('Invalid or missing message data', { webhookData: JSON.stringify(webhookData).substring(0, 500) });
      return {};
    }
    const messageId = message.id;

    // 验证对话对象
    if (!dialogue || !dialogue.id) {
      logger.error('Invalid or missing dialogue data', { webhookData: JSON.stringify(webhookData).substring(0, 500) });
      return {};
    }
    const dialogueId = dialogue.id;

    // 验证接收者ID
    if (!dialogue.relationships?.recipient?.data?.id) {
      logger.error('Missing recipient ID in dialogue', { dialogue: JSON.stringify(dialogue).substring(0, 500) });
      return {};
    }
    const recipientId = dialogue.relationships.recipient.data.id;

    // 验证代理ID
    const brandWhatsappId = dialogue.relationships?.agent?.data?.id;
    if (!brandWhatsappId) {
      logger.warn('Missing brandWhatsappId (agent ID) in dialogue', {
        dialogue: JSON.stringify(dialogue).substring(0, 500)
      });
    }

    // 记录提取的数据
    logger.debug('Extracted webhook data:', { messageId, dialogueId, recipientId, brandWhatsappId });
    return { message, dialogue, messageId, dialogueId, recipientId, brandWhatsappId };
  }
  /**
   * Extract message content from new format
   * @param {Object} message - Message object
   * @returns {Object} Extracted content and metadata
   */
  extractMessageContent(message) {
    try {
      const textContent = message.attributes?.content?.find(item => item.displayType === 'text');
      return {
        messageContent: textContent?.attrs?.text || '',
        externalID: textContent?.payload?.externalID,
        messageType: textContent ? 'TEXT' : 'default'
      };
    } catch (error) {
      logger.error('Error extracting message content:', error);
      return { messageContent: '', externalID: null, messageType: 'default' };
    }
  }

  /**
   * Get customer phone number from API
   * @param {string} recipientId - Recipient ID
   * @returns {Promise<string|null>} - Customer phone number or null
   */
  async getCustomerPhone(recipientId) {
    try {
      const token = await whatsappService.getAccessToken();
      const response = await axios.get(`${whatsappService.apiUrl}/customers/${recipientId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/vnd.api+json',
          Accept: 'application/vnd.api+json'
        }
      });
      const phoneNumber = response.data?.data?.attributes?.phones?.[0];
      if (!phoneNumber) {
        logger.warn(`Unable to get phone number from recipient ID: ${recipientId}`);
        return null;
      }
      return phoneNumber;
    } catch (error) {
      logger.error(`Failed to get customer phone number (ID: ${recipientId}):`, {
        error: error.message,
        status: error.response?.status,
        data: error.response?.data
      });
      return null;
    }
  }

  /**
   * Get or create a session for the dialogue
   * @param {string} dialogueId - Dialogue ID
   * @param {string} recipientId - Recipient ID
   * @param {string} brandWhatsappId - Brand WhatsApp ID (agent ID)
   * @returns {Object} - Session object
   */
  async getOrCreateSession(dialogueId, recipientId, brandWhatsappId) {
    let session = await sessionService.getSession(dialogueId);
    if (!session) {
    // Get customer phone number
    const customerPhone = await this.getCustomerPhone(recipientId);
    if (!customerPhone) {
      logger.error('Failed to get customer phone number', { recipientId });
      return null;
    }

    // Get brand data
    const brandRef = restaurantStore.getBrandRef(brandWhatsappId);
    logger.debug('Brand reference:', { brandRef });
    // Fetch customer information if we have brand
    let customerInfo = null;
    if (brandRef) {
      try {
        customerInfo = await fetchCustomerInfo(customerPhone, brandRef.id);
      } catch (error) {
        logger.error('Failed to fetch customer info:', error);
      }
    }

      // 自动选择餐厅
      let selectedRestaurantRef = null;
      let isRestaurantSelected = false;
      let selectedAddressIndex = null;
      let isAddressSelected = false;

      if (customerInfo && brandRef) {
        // 如果有历史订单，选择最新订单的餐厅
        if (customerInfo.orderHistory && customerInfo.orderHistory.length > 0) {
          const lastOrder = customerInfo.orderHistory[0];
          selectedRestaurantRef = restaurantStore.getRestaurantRef(lastOrder.restaurantId);
          logger.debug('Auto-selected restaurant from order history', {
            restaurantId: selectedRestaurantRef?.id,
            restaurantName: selectedRestaurantRef?.name
          });

          // 如果有地址信息，从最新订单中获取地址作为默认地址
          if (lastOrder.deliveryAddressId && customerInfo.addresses?.length > 0) {
            // 查找对应的地址在数组中的索引
            const addressIndex = customerInfo.addresses.findIndex(
              addr => addr.addressId === lastOrder.deliveryAddressId
            );

            // 如果找到有效的索引，设置为选中的地址
            if (addressIndex !== undefined && addressIndex >= 0) {
              selectedAddressIndex = addressIndex;
              isAddressSelected = true;

              logger.debug('Auto-selected address from order history', {
                addressIndex,
                addressId: customerInfo.addresses[addressIndex].addressId,
                address: customerInfo.addresses[addressIndex].formattedAddress
              });
            }
          }
        }
        // 如果没有历史订单，但品牌只有一个餐厅，选择该餐厅
        else if (brandRef.restaurants?.length === 1) {
          selectedRestaurantRef = restaurantStore.getRestaurantRef(brandRef.restaurants[0]._id.toString()); // TDODO
          logger.debug('Auto-selected the only restaurant in brand', {
            restaurantId: selectedRestaurantRef?.id,
            restaurantName: selectedRestaurantRef?.name
          });
        }

        // 如果成功选择了餐厅，设置 isRestaurantSelected = true
        if (selectedRestaurantRef) {
          isRestaurantSelected = true;
        }
      }

      // Create a new session
      const sessionData = {
        recipientId,
        customerPhone,
        brandWhatsappId,
        brandRef,
        context: {
          customer: customerInfo,
          selectedRestaurantRef: selectedRestaurantRef,
          selectedAddressIndex: selectedAddressIndex,
          welcomeMessageSent: false,
          isRestaurantSelected: isRestaurantSelected,
          isAddressSelected: isAddressSelected,
          cartReceived: false
        }
      };
      logger.debug('Creating new session with data:', { dialogueId, sessionData });

      const session = await sessionService.createSession(dialogueId, sessionData);
      if (!session) {
        logger.error('Failed to create new session', { dialogueId, recipientId, customerPhone, brandWhatsappId });
        return null;
      }

      // 验证创建的session
      if (!session.dialogueId || session.dialogueId !== dialogueId) {
        logger.error('Created session has invalid dialogueId', {
          expected: dialogueId,
          actual: session.dialogueId,
          session
        });
        return null;
      }
    }
    // Update session with any missing information
    else {
      const updates = {};
      if (session.recipientId !== recipientId) {
        updates.recipientId = recipientId;
        const customerPhone = await this.getCustomerPhone(recipientId);
        if (!customerPhone) {
          logger.error('Failed to get customer phone number', { recipientId });
          return null;
        } else {
          updates.customerPhone = customerPhone;
        }
      }
      if (session.brandWhatsappId !== brandWhatsappId) {
        updates.brandWhatsappId = brandWhatsappId;

        // 如果 brandWhatsappId 变化, 更新 brandRef
        const brandRef = restaurantStore.getBrandRef(brandWhatsappId);
        if (brandRef) {
          updates.brandRef = brandRef;
          // Update customer info with new brand
          try {
            const customerInfo = await fetchCustomerInfo(session.customerPhone, brandRef.id);
            if (customerInfo) {
              updates.context = {
                ...session.context,
                customer: customerInfo
              };
            }
          } catch (error) {
            logger.error('Failed to update customer info:', error);
          }
        }
      } else if (!session.brandRef) {
        // 如果 brandRef 为空，尝试获取
        const brandRef = restaurantStore.getBrandRef(brandWhatsappId);
        if (brandRef) {
          updates.brandRef = brandRef;
        }
      }

      if (Object.keys(updates).length > 0) {
        const success = await sessionService.updateSession(dialogueId, updates);
        if (!success) {
          logger.error('Failed to update session', { dialogueId, updates });
          return null;
        }
        logger.debug('Updated session information:', { dialogueId, updates });

        // Fetch the updated session
        session = await sessionService.getSession(dialogueId);
        if (!session || !session.dialogueId || session.dialogueId !== dialogueId) {
          logger.error('Updated session is invalid', {
            dialogueId,
            session
          });
          return null;
        }
      }
      // logger.debug('Session state:', { dialogueId, session })
    }
    return session;
  }

  /**
   * Process message status update from webhook
   * @param {Object} webhookData - The webhook data
   */
  async processMessageStatusUpdate(webhookData) {
    try {
      const messageStatus = webhookData.included?.find(item => item.type === 'message-statuses');
      if (!messageStatus) {
        logger.error('Missing message status in webhook data', { webhookData });
        return;
      }
      const messageId = messageStatus.attributes?.messageId;
      const status = messageStatus.attributes?.status;
      logger.debug(`Message ${messageId} status updated to ${status}`);
      // Can implement additional logic based on message status as needed
    } catch (error) {
      logger.error('Error processing message status update:', {
        error: error.message,
        stack: error.stack?.split('\n').slice(0, 3).join('\n')
      });
    }
  }

  /**
   * Process dialogue status update from webhook
   * @param {Object} webhookData - The webhook data
   */
  async processDialogueStatusUpdate(webhookData) {
    try {
      const dialogue = webhookData.included?.find(item => item.type === 'dialogues');
      if (!dialogue) {
        logger.error('Missing dialogue in webhook data', { webhookData });
        return;
      }
      const dialogueId = dialogue.id;
      const status = dialogue.attributes?.status;
      logger.debug(`Dialogue ${dialogueId} status updated to ${status}`);
      if (status === 'CLOSED') {
        const session = await sessionService.getSession(dialogueId);
        if (session) {
          await sessionService.updateSession(dialogueId, {
            isActive: false,
            closedAt: new Date().toISOString()
          });
          logger.debug(`Marked session for dialogue ${dialogueId} as inactive`);
        }
      }
    } catch (error) {
      logger.error('Error processing dialogue status update:', {
        error: error.message,
        stack: error.stack?.split('\n').slice(0, 3).join('\n')
      });
    }
  }
}

const webhookController = new WebhookController();

module.exports = {
  handleIncoming: webhookController.handleIncoming.bind(webhookController),
  processIncomingMessage: webhookController.processIncomingMessage.bind(webhookController),
  processMessageStatusUpdate: webhookController.processMessageStatusUpdate.bind(webhookController),
  processDialogueStatusUpdate: webhookController.processDialogueStatusUpdate.bind(webhookController)
};
