const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
var randomstring = require('randomstring');
const mongoose = require('mongoose');
const Restaurant = require('../../models/restaurant');
const Owner = require('../../models/owner');
const Offer = require('../../models/offer');
const Order = require('../../models/order');
const Point = require('../../models/point');
const Sections = require('../../models/section');
const logger = require('../../helpers/logger');
const whatsappService = require('../../whatsapp/services/whatsappService');
const sessionService = require('../../whatsapp/services/sessionService');
const restaurantStore = require('../../whatsapp/services/restaurantStore');
const config = require('../../config');
const Zone = require('../../models/zone');
const Customer = require('../../models/customer');
const { USER_ROLES } = require('../permissions/constants');
const { sendNotificationToCustomerWeb } = require('../../helpers/firebase-web-notifications');
const {
  transformRestaurant,
  transformOwner,
  transformRestaurants,
  transformOrder,
  transformMinimalRestaurantData,
  transformMinimalRestaurants
} = require('./merge');
const { ORDER_STATUS, SHOP_TYPE, getThirtyDaysAgo } = require('../../helpers/enum');
const { publishToZoneRiders, publishOrder, publishToUser } = require('../../helpers/pubsub');
const { sendNotificationToZoneRiders } = require('../../helpers/notifications');
const { sendNotificationToUser, sendNotificationToRider } = require('../../helpers/notifications');

module.exports = {
  Query: {
    nearByRestaurants: async (_, args) => {
      console.log('nearByRestaurants', args);

      try {
        const { shopType } = args;
        const query = {
          isActive: true,
          isAvailable: true,
          deliveryBounds: {
            $geoIntersects: {
              $geometry: {
                type: 'Point',
                coordinates: [Number(args.longitude), Number(args.latitude)]
              }
            }
          }
        };
        if (shopType) {
          query.shopType = shopType;
        }
        const restaurants = await Restaurant.find(query);

        if (!restaurants.length) {
          return {
            restaurants: [],
            sections: [],
            offers: []
          };
        }
        // TODO: do something about offers too w.r.t zones
        const offers = await Offer.find({ isActive: true, enabled: true });

        // Find restaurants containing sections / offers
        const sectionArray = [...new Set([...restaurants.map(res => res.sections)].flat())];
        const sections = await Sections.find({
          _id: { $in: sectionArray },
          enabled: true
        });

        const result = {
          restaurants: await restaurants.map(transformRestaurant),
          sections: sections.map(sec => ({
            _id: sec.id,
            name: sec.name,
            restaurants: sec.restaurants
          })),
          offers: offers.map(o => ({
            ...o._doc,
            _id: o.id
          }))
        };
        return result;
      } catch (err) {
        console.log(err);
        throw err;
      }
    },
    nearByRestaurantsPreview: async (_, args) => {
      console.log('nearByRestaurantsPreview', args);
      try {
        const { shopType } = args;
        const query = {
          isActive: true,
          isAvailable: true,
          deliveryBounds: {
            $geoIntersects: {
              $geometry: {
                type: 'Point',
                coordinates: [Number(args.longitude), Number(args.latitude)]
              }
            }
          }
        };
        if (shopType) {
          query.shopType = shopType;
        }
        const restaurants = await Restaurant.find(query);

        if (!restaurants.length) {
          return {
            restaurants: [],
            sections: [],
            offers: []
          };
        }
        // TODO: do something about offers too w.r.t zones
        const offers = await Offer.find({ isActive: true, enabled: true });

        // Find restaurants containing sections / offers
        const sectionArray = [...new Set([...restaurants.map(res => res.sections)].flat())];
        const sections = await Sections.find({
          _id: { $in: sectionArray },
          enabled: true
        });

        const result = {
          restaurants: await restaurants.map(transformMinimalRestaurantData),
          sections: sections.map(sec => ({
            _id: sec.id,
            name: sec.name,
            restaurants: sec.restaurants
          })),
          offers: offers.map(o => ({
            ...o._doc,
            _id: o.id
          }))
        };
        return result;
      } catch (err) {
        throw err;
      }
    },
    restaurantList: async _ => {
      console.log('restaurantList');
      try {
        const allRestaurants = await Restaurant.find({ address: { $ne: null } });
        return transformRestaurants(allRestaurants);
      } catch (error) {
        throw error;
      }
    },
    restaurantListPreview: async _ => {
      console.log('restaurantListPreview');
      try {
        const allRestaurants = await Restaurant.find({ address: { $ne: null } });
        return transformMinimalRestaurants(allRestaurants);
      } catch (error) {
        throw error;
      }
    },
    restaurantByOwner: async (_, args, { req }) => {
      console.log('restaurantByOwner');
      try {
        const id = args.id || req.userId;
        const owner = await Owner.findById(id);
        return transformOwner(owner);
      } catch (e) {
        throw e;
      }
    },
    restaurants: async _ => {
      console.log('restaurants');
      try {
        const restaurants = await Restaurant.find();
        return transformRestaurants(restaurants);
      } catch (e) {
        throw e;
      }
    },
    restaurantsPreview: async _ => {
      console.log('restaurantsPreview');
      try {
        const restaurants = await Restaurant.find();
        return transformMinimalRestaurants(restaurants);
      } catch (e) {
        throw e;
      }
    },
    restaurant: async (_, args, { req }) => {
      console.log('restaurant', args);
      try {
        const filters = {};
        if (args.slug) {
          filters.slug = args.slug;
        } else if (args.id) {
          filters._id = args.id;
        } else if (req.restaurantId) {
          filters._id = req.restaurantId;
        } else {
          throw new Error('Invalid request, restaurant id not provided');
        }
        const restaurant = await Restaurant.findOne(filters);
        if (!restaurant) throw Error('Restaurant not found');
        return transformRestaurant(restaurant);
      } catch (e) {
        throw e;
      }
    },
    restaurantPreview: async (_, args, { req }) => {
      console.log('restaurantPreview', args);
      try {
        const filters = {};
        if (args.slug) {
          filters.slug = args.slug;
        } else if (args.id) {
          filters._id = args.id;
        } else if (req.restaurantId) {
          filters._id = req.restaurantId;
        } else {
          throw new Error('Invalid request, restaurant id not provided');
        }
        const restaurant = await Restaurant.findOne(filters);
        if (!restaurant) throw Error('Restaurant not found');
        return transformMinimalRestaurantData(restaurant);
      } catch (e) {
        throw e;
      }
    },
    restaurantOrders: async (_, args, { req }) => {
      console.log('restaurantOrders', req.restaurantId);
      const date = new Date();
      date.setDate(date.getDate() - 1);
      const orders = await Order.find({
        restaurant: req.restaurantId,
        createdAt: {
          $gte: `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
        }
      }).sort({ createdAt: 'descending' }); // today and yesterday instead of limit 50
      return orders.map(transformOrder);
    },
    recentOrderRestaurants: async (_, args, { req }) => {
      console.log('recentOrderRestaurants', args, req.userId);
      const { longitude, latitude } = args;
      if (!req.isAuth) throw new Error('Unauthenticated');
      // selects recent orders
      const recentRestaurantIds = await Order.find({ user: req.userId })
        .select('restaurant')
        .sort({ createdAt: -1 })
        .limit(100)
        .lean();
      // if no orders, no restaurant, returns empty
      if (!recentRestaurantIds.length) return [];
      const restaurantIds = recentRestaurantIds.map(r => r.restaurant.toString());
      // finds restaurants by id, also make sures restaurants delivers in the area.
      const restaurants = await Restaurant.find({
        $and: [
          {
            id: {
              $in: restaurantIds
            }
          },
          {
            isActive: true,
            isAvailable: true,
            deliveryBounds: {
              $geoIntersects: {
                $geometry: {
                  type: 'Point',
                  coordinates: [Number(longitude), Number(latitude)]
                }
              }
            }
          }
        ]
      });
      return restaurants.map(transformRestaurant);
    },
    recentOrderRestaurantsPreview: async (_, args, { req }) => {
      console.log('recentOrderRestaurantsPreview', args, req.userId);
      const { longitude, latitude } = args;
      if (!req.isAuth) throw new Error('Unauthenticated');
      // selects recent orders
      const recentRestaurantIds = await Order.find({ user: req.userId })
        .select('restaurant')
        .sort({ createdAt: -1 })
        .limit(100)
        .lean();
      // if no orders, no restaurant, returns empty
      if (!recentRestaurantIds.length) return [];
      const restaurantIds = recentRestaurantIds.map(r => r.restaurant.toString());
      // finds restaurants by id, also make sures restaurants delivers in the area.
      const restaurants = await Restaurant.find({
        $and: [
          {
            id: {
              $in: restaurantIds
            }
          },
          {
            isActive: true,
            isAvailable: true,
            deliveryBounds: {
              $geoIntersects: {
                $geometry: {
                  type: 'Point',
                  coordinates: [Number(longitude), Number(latitude)]
                }
              }
            }
          }
        ]
      });
      return restaurants.map(transformMinimalRestaurantData);
    },
    mostOrderedRestaurants: async (_, args, { req }) => {
      console.log('mostOrderedRestaurants', args, req.userId);
      const { longitude, latitude } = args;
      const restaurants = await Restaurant.aggregate([
        {
          $match: {
            isActive: true,
            isAvailable: true,
            deliveryBounds: {
              $geoIntersects: {
                $geometry: {
                  type: 'Point',
                  coordinates: [Number(longitude), Number(latitude)]
                }
              }
            }
          }
        },
        {
          $lookup: {
            from: 'orders',
            localField: '_id',
            foreignField: 'restaurant',
            pipeline: [
              {
                $match: {
                  createdAt: { $gte: getThirtyDaysAgo() }
                }
              }
            ],
            as: 'orders'
          }
        },
        {
          $addFields: {
            orderCount: { $size: '$orders' }
          }
        },
        {
          $sort: { orderCount: -1 }
        },
        {
          $limit: 20
        }
      ]).exec();

      return restaurants.map(r => transformRestaurant(new Restaurant(r)));
    },
    mostOrderedRestaurantsPreview: async (_, args, { req }) => {
      console.log('mostOrderedRestaurantsPreview', args, req.userId);
      const { longitude, latitude } = args;
      const restaurants = await Restaurant.aggregate([
        {
          $match: {
            isActive: true,
            isAvailable: true,
            deliveryBounds: {
              $geoIntersects: {
                $geometry: {
                  type: 'Point',
                  coordinates: [Number(longitude), Number(latitude)]
                }
              }
            }
          }
        },
        {
          $lookup: {
            from: 'orders',
            localField: '_id',
            foreignField: 'restaurant',
            pipeline: [
              {
                $match: {
                  createdAt: { $gte: getThirtyDaysAgo() }
                }
              }
            ],
            as: 'orders'
          }
        },
        {
          $addFields: {
            orderCount: { $size: '$orders' }
          }
        },
        {
          $sort: { orderCount: -1 }
        },
        {
          $limit: 20
        }
      ]).exec();

      return restaurants.map(r => transformMinimalRestaurantData(new Restaurant(r)));
    },
    relatedItems: async (_, args, { req }) => {
      console.log('relatedItems', args, req.userId);
      try {
        const { itemId, restaurantId } = args;
        const items = await Order.aggregate([
          {
            $match: {
              $and: [
                { 'items.food': itemId },
                { restaurant: mongoose.Types.ObjectId(restaurantId) },
                { createdAt: { $gte: getThirtyDaysAgo() } }
              ]
            }
          },
          {
            $unwind: '$items'
          },
          {
            $match: {
              'items.food': { $ne: itemId }
            }
          },
          {
            $group: {
              _id: '$items.food',
              count: { $sum: 1 }
            }
          },
          {
            $sort: { count: -1 }
          },
          {
            $limit: 10
          }
        ]).exec();

        return items.map(item => item._id);
      } catch (error) {
        console.log('relatedItems', error);
        throw error;
      }
    },
    popularItems: async (_, args) => {
      console.log('popularItems', args);
      try {
        const { restaurantId } = args;
        const result = await Order.aggregate([
          {
            $match: {
              $and: [{ restaurant: mongoose.Types.ObjectId(restaurantId) }, { createdAt: { $gte: getThirtyDaysAgo() } }]
            }
          },
          { $unwind: '$items' },
          { $group: { _id: { id: '$items.food' }, count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]).exec();
        return result.map(({ _id: { id }, count }) => ({ id, count }));
      } catch (error) {
        console.log('popularItems errored', error);
      }
    },
    topRatedVendors: async (_, args, { req }) => {
      console.log('topRatedVendors', args);
      try {
        const { longitude, latitude } = args;
        const restaurants = await Restaurant.aggregate([
          {
            $match: {
              isActive: true,
              isAvailable: true,
              deliveryBounds: {
                $geoIntersects: {
                  $geometry: {
                    type: 'Point',
                    coordinates: [Number(longitude), Number(latitude)]
                  }
                }
              }
            }
          },
          {
            $lookup: {
              from: 'reviews',
              localField: '_id',
              foreignField: 'restaurant',
              pipeline: [
                {
                  $match: {
                    createdAt: { $gte: getThirtyDaysAgo() }
                  }
                }
              ],
              as: 'reviews'
            }
          },
          {
            $addFields: {
              averageRating: { $ifNull: [{ $avg: '$reviews.rating' }, 0] } // Calculate the average of the 'rating' property
            }
          },
          {
            $sort: { averageRating: -1 }
          },
          {
            $limit: 20
          }
        ]).exec();
        return restaurants.map(restaurant => transformRestaurant(new Restaurant(restaurant)));
      } catch (error) {
        console.log('topRatedVendors error', error);
      }
    },
    topRatedVendorsPreview: async (_, args, { req }) => {
      console.log('topRatedVendorsPreview', args);
      try {
        const { longitude, latitude } = args;
        const restaurants = await Restaurant.aggregate([
          {
            $match: {
              isActive: true,
              isAvailable: true,
              deliveryBounds: {
                $geoIntersects: {
                  $geometry: {
                    type: 'Point',
                    coordinates: [Number(longitude), Number(latitude)]
                  }
                }
              }
            }
          },
          {
            $lookup: {
              from: 'reviews',
              localField: '_id',
              foreignField: 'restaurant',
              pipeline: [
                {
                  $match: {
                    createdAt: { $gte: getThirtyDaysAgo() }
                  }
                }
              ],
              as: 'reviews'
            }
          },
          {
            $addFields: {
              averageRating: { $ifNull: [{ $avg: '$reviews.rating' }, 0] } // Calculate the average of the 'rating' property
            }
          },
          {
            $sort: { averageRating: -1 }
          },
          {
            $limit: 20
          }
        ]).exec();
        return restaurants.map(restaurant => transformMinimalRestaurantData(new Restaurant(restaurant)));
      } catch (error) {
        console.log('topRatedVendors error', error);
      }
    }
  },
  Mutation: {
    createRestaurant: async (_, args, { req }) => {
      console.log('createRestanrant', args);
      try {
        if (!req.userId) throw new Error('Unauthenticated');
        const restaurantExists = await Restaurant.exists({
          name: { $regex: new RegExp('^' + args.restaurant.name + '$', 'i') }
        });
        if (restaurantExists) {
          throw Error('Restaurant by this name already exists');
        }
        const owner = await Owner.findById(args.owner);
        if (!owner) throw new Error('Owner does not exist');
        const orderPrefix = randomstring.generate({
          length: 5,
          capitalization: 'uppercase'
        });

        // 加密密码
        const hashedPassword = await bcrypt.hash(args.restaurant.password, 12);

        const restaurant = new Restaurant({
          name: args.restaurant.name,
          address: args.restaurant.address,
          image: args.restaurant.image,
          logo: args.restaurant.logo,
          orderPrefix: orderPrefix,
          slug: args.restaurant.name.toLowerCase().split(' ').join('-'),
          username: args.restaurant.username,
          password: hashedPassword,
          owner: args.owner,
          tax: args.salesTax,
          cuisines: args.restaurant.cuisines ?? [],
          shopType: args.restaurant.shopType || SHOP_TYPE.RESTAURANT, //  default value 'restaurant' for backward compatibility
          restaurantUrl: args.restaurant.restaurantUrl,
          phone: args.restaurant.phone,
          deliveryCostType: args.restaurant.deliveryCostType || 'fixed',
          deliveryCostRate: args.restaurant.deliveryCostRate,
          deliveryCostMin: args.restaurant.deliveryCostMin
        });
        console.log('New Restaurant: ', restaurant);

        const result = await restaurant.save();
        owner.restaurants.push(result.id);
        await owner.save();
        return transformRestaurant(result);
      } catch (err) {
        throw err;
      }
    },
    editRestaurant: async (_, args) => {
      console.log('editRestaurant');
      try {
        const restaurantByNameExists = await Restaurant.findOne({
          name: { $regex: new RegExp('^' + args.restaurant.name + '$', 'i') },
          // name: { $text: { $search: args.restaurant.name } },
          _id: { $ne: args.restaurant._id }
        })
          .select({ _id: 1 })
          .lean();

        if (restaurantByNameExists) {
          throw new Error('Restaurant by this name already exists');
        }
        if (args.restaurant.username) {
          const restaurantExists = await Restaurant.findOne({
            username: args.restaurant.username
          });

          if (restaurantExists && restaurantExists.id !== args.restaurant._id) {
            throw new Error('Username already taken');
          }
        }
        if (args.restaurant.orderPrefix) {
          const restaurantExists = await Restaurant.find({
            orderPrefix: args.restaurant.orderPrefix
          });
          if (restaurantExists.length > 0) {
            if (restaurantExists.length > 1) {
              throw new Error('Order Prefix already taken');
            } else if (restaurantExists[0].id !== args.restaurant._id) {
              throw new Error('Order Prefix already taken');
            }
          }
        }

        const restaurant = await Restaurant.findOne({
          _id: args.restaurant._id
        });
        restaurant.name = args.restaurant.name;
        restaurant.address = args.restaurant.address;
        restaurant.image = args.restaurant.image;
        restaurant.logo = args.restaurant.logo;
        restaurant.orderPrefix = args.restaurant.orderPrefix;
        restaurant.isActive = true;
        restaurant.username = args.restaurant.username;
        restaurant.deliveryTime = args.restaurant.deliveryTime;
        restaurant.minimumOrder = args.restaurant.minimumOrder;

        // 如果提供了新密码，则加密后保存
        if (args.restaurant.password) {
          restaurant.password = await bcrypt.hash(args.restaurant.password, 12);
        }

        restaurant.slug = args.restaurant.name.toLowerCase().split(' ').join('-');
        restaurant.tax = args.restaurant.salesTax;
        restaurant.shopType = args.restaurant.shopType;
        restaurant.cuisines = args.restaurant.cuisines;
        restaurant.restaurantUrl = args.restaurant.restaurantUrl;
        restaurant.phone = args.restaurant.phone;
        restaurant.deliveryCostType = args.restaurant.deliveryCostType || 'fixed';
        restaurant.deliveryCostRate = args.restaurant.deliveryCostRate;
        restaurant.deliveryCostMin = args.restaurant.deliveryCostMin;

        const result = await restaurant.save();

        return transformRestaurant(result);
      } catch (err) {
        throw err;
      }
    },
    deleteRestaurant: async (_, { id }, { req }) => {
      console.log('deleteRestaurant', req.userId);
      try {
        const owner = await Owner.findOne({
          restaurants: mongoose.Types.ObjectId(id)
        });
        if (!owner) throw new Error('Owner does not exist');
        if (!owner.isActive) throw new Error('Owner was deleted');
        const restaurant = await Restaurant.findById(id);
        restaurant.isActive = !restaurant.isActive;
        const result = await restaurant.save();
        return transformRestaurant(result);
      } catch (err) {
        throw err;
      }
    },
    restaurantLogin: async (_, { username, password }) => {
      console.log('restaurantLogin');

      // 查找餐厅
      const restaurant = await Restaurant.findOne({ username });
      if (!restaurant) {
        throw new Error('Invalid credentials');
      }

      // 验证密码
      const isEqual = await bcrypt.compare(password, restaurant.password);
      if (!isEqual) {
        throw new Error('Invalid credentials');
      }

      // 生成JWT token，包含userType
      const token = jwt.sign(
        {
          restaurantId: restaurant.id,
          userType: USER_ROLES.RESTAURANT
        },
        config.JWT_SECRET
      );

      return { token, restaurantId: restaurant.id };
    },
    acceptOrder: async (_, args, { req }) => {
      var newDateObj = await new Date(Date.now() + (parseInt(args.time) || 0) * 60000);
      logger.debug('Order preparation time', { preparationTime: newDateObj });
      if (!req.restaurantId) {
        throw new Error('Unauthenticated!');
      }
      try {
        const order = await Order.findById(args._id);
        const status = ORDER_STATUS.ACCEPTED;
        order.orderStatus = status;
        const restaurant = await Restaurant.findById(req.restaurantId);
        order.preparationTime = newDateObj;
        order.completionTime = new Date(Date.now() + restaurant.deliveryTime * 60 * 1000);
        order.acceptedAt = new Date();
        const result = await order.save();
        const customer = await Customer.findOne({ customerId: result.customerId });
        const transformedOrder = await transformOrder(result);

        // 发送WhatsApp通知
        if (customer && customer.phone) {
          try {
            // 查找客户的WhatsApp会话
            const sessions = await sessionService.findSessionsByCustomerPhone(customer.phone);

            if (sessions && sessions.length > 0) {
              // TODO: error handling
              // 查找与当前餐厅品牌匹配的会话
              let matchingSession = null;

              // 直接使用订单的品牌ID查找会话
              if (order.restaurantBrandId) {
                const brandId = order.restaurantBrandId.toString();
                matchingSession = sessions.find(session => session.brandRef && session.brandRef.id === brandId);

                logger.debug('Looking for session by brand', {
                  brandId,
                  found: !!matchingSession,
                  sessionsCount: sessions.length
                });
              }

              // 如果没找到匹配的会话，使用最近的会话
              if (!matchingSession) {
                logger.error('no session found for acceptOrder');
              }

              if (matchingSession) {
                // 构建消息内容
                let message = `Your order #${order.orderId} has been accepted by ${order.restaurantName}.\n`;

                // 只有非自取订单才添加预计送达时间
                if (!order.isPickedUp) {
                  // 格式化预计送达时间
                  const deliveryTime = new Date(order.completionTime);
                  const formattedTime = deliveryTime.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  });

                  message += `Estimated delivery time: ${formattedTime}.\n`;
                } else {
                  message += `Your order will be ready for pickup soon.\n`;
                }

                message += `Thank you for your order!`;

                // 发送WhatsApp消息
                await whatsappService.sendDialogueText(matchingSession.dialogueId, message);

                logger.info('Order accepted notification sent', {
                  customerPhone: customer.phone,
                  orderId: order.orderId,
                  dialogueId: matchingSession.dialogueId
                });
              }
            }
          } catch (error) {
            logger.error('Error sending WhatsApp notification', {
              error: error.message,
              stack: error.stack,
              customerPhone: customer.phone,
              orderId: order.orderId
            });
            // 不抛出错误，因为通知失败不应影响订单处理
          }
        }

        if (!transformedOrder.isPickedUp) {
          /*
          publishToZoneRiders(order.zone.toString(), transformedOrder, 'new')
          sendNotificationToZoneRiders(order.zone.toString(), transformedOrder)
          */
        }
        /*
        publishToUser(result.user.toString(), transformedOrder, 'update')
        sendNotificationToCustomerWeb(
          user.notificationTokenWeb,
          `Order status: ${result.orderStatus}`,
          `Order ID ${result.orderId}`
        )
        publishOrder(transformedOrder)
        sendNotificationToUser(result.user.toString(), transformedOrder) */
        return transformedOrder;
      } catch (err) {
        logger.error('Error in acceptOrder', { error: err.message, stack: err.stack });
        throw err;
      }
    },
    cancelOrder: async (_, args, { req }) => {
      const logger = require('../../helpers/logger');
      const refundService = require('../../services/refundService');
      const { REFUND_TYPE } = require('../../helpers/enum');

      logger.debug('Processing order cancellation with refund', { orderId: args._id, reason: args.reason });

      if (!req.restaurantId) {
        throw new Error('Unauthenticated!');
      }
      try {
        const order = await Order.findById(args._id);
        if (!order) {
          throw new Error('Order not found');
        }

        // 验证权限
        if (order.restaurantId !== req.restaurantId) {
          throw new Error('Access denied');
        }

        // 处理退款（Order记录存在意味着已支付）
        if (order.paymentMethod === 'STRIPE') {
          logger.info('Processing full refund for cancelled Stripe order', {
            orderId: order.orderId,
            amount: order.orderAmount,
            reason: args.reason
          });

          try {
            await refundService.initiateRefund(
              order._id,
              null, // 全额退款不需要指定金额
              args.reason,
              null, // 取消订单不需要原因文本
              REFUND_TYPE.FULL,
              req.restaurantId
            );
            logger.info('Refund initiated successfully for cancelled order', { orderId: order.orderId });
          } catch (refundError) {
            logger.error('Refund failed for cancelled order', {
              orderId: order.orderId,
              error: refundError.message
            });
            // 继续取消订单，但记录退款失败
          }
        } else if (order.paymentMethod === 'PAYPAL') {
          logger.info('PayPal refund not implemented yet', { orderId: order.orderId });
          // TODO: 实现PayPal退款逻辑
        } else if (order.paymentMethod === 'COD') {
          logger.info('COD order cancelled, no refund needed', { orderId: order.orderId });
          // COD订单无需退款
        }

        // 更新订单状态
        const status = ORDER_STATUS.CANCELLED;
        order.orderStatus = status;
        order.reason = args.reason;
        order.cancelledAt = new Date();
        const result = await order.save();
        const customer = await Customer.findOne({ customerId: result.customerId });
        const transformedOrder = await transformOrder(result);

        // 发送WhatsApp通知
        if (customer && customer.phone) {
          try {
            // 获取餐厅信息以提供联系方式
            const restaurant = await Restaurant.findById(order.restaurantId);

            // 查找客户的WhatsApp会话
            const sessions = await sessionService.findSessionsByCustomerPhone(customer.phone);

            if (sessions && sessions.length > 0) {
              // TODO: error handling
              // 查找与当前餐厅品牌匹配的会话
              let matchingSession = null;

              // 直接使用订单的品牌ID查找会话
              if (order.restaurantBrandId) {
                const brandId = order.restaurantBrandId.toString();
                matchingSession = sessions.find(session => session.brandRef && session.brandRef.id === brandId);

                logger.debug('Looking for session by brand', {
                  brandId,
                  found: !!matchingSession,
                  sessionsCount: sessions.length
                });
              }

              // 如果没找到匹配的会话，使用最近的会话
              if (!matchingSession) {
                logger.error('no session found for cancelOrder');
              }

              if (matchingSession) {
                // 构建消息内容
                let message = `We're sorry, but your order #${order.orderId} has been cancelled.\n`;

                // 添加取消原因（如果有）
                if (order.reason) {
                  message += `Reason: ${order.reason}\n`;
                }

                // 添加餐厅联系信息
                if (restaurant && restaurant.phone) {
                  message +=
                    `If you have any questions, please contact the restaurant directly:\n` +
                    `${restaurant.name}: ${restaurant.phone}\n`;
                }

                // 添加支付状态信息
                if (order.paymentStatus === 'PAID') {
                  message += `Your payment will be refunded according to our refund policy.`;
                }

                // 发送WhatsApp消息
                await whatsappService.sendDialogueText(matchingSession.dialogueId, message);

                logger.info('Order cancellation notification sent', {
                  customerPhone: customer.phone,
                  orderId: order.orderId,
                  dialogueId: matchingSession.dialogueId
                });
              }
            }
          } catch (error) {
            logger.error('Error sending WhatsApp notification', {
              error: error.message,
              stack: error.stack,
              customerPhone: customer.phone,
              orderId: order.orderId
            });
            // 不抛出错误，因为通知失败不应影响订单处理
          }
        }

        /*
        publishToUser(result.user.toString(), transformedOrder, 'update')
        publishOrder(transformedOrder)
        if (result.rider) {
          sendNotificationToRider(result.rider.toString(), transformedOrder)
        }

        sendNotificationToUser(result.user, transformedOrder)
        sendNotificationToCustomerWeb(
          user.notificationTokenWeb,
          `Order status: ${result.orderStatus}`,
          `Order ID ${result.orderId}`
        ) */
        return transformedOrder;
      } catch (err) {
        logger.error('Error in cancelOrder', { error: err.message, stack: err.stack });
        throw err;
      }
    },
    saveRestaurantToken: async (_, args, { req }) => {
      console.log('saveRestaurantToken', req.restaurantId, args);
      try {
        const restaurant = await Restaurant.findById(req.restaurantId);
        if (!restaurant) throw new Error('Restaurant does not exist');
        restaurant.notificationToken = args.token;
        restaurant.enableNotification = args.isEnabled;
        const result = await restaurant.save();

        return transformRestaurant(result);
      } catch (error) {
        console.log('error', error);
      }
    },
    updateTimings: async (_, args) => {
      console.log('updateTimings', args);
      try {
        const restaurant = await Restaurant.findById(args.id);
        restaurant.openingTimes = args.openingTimes;
        const result = await restaurant.save();
        return transformRestaurant(result);
      } catch (err) {
        throw err;
      }
    },
    toggleAvailability: async (_, args, { req }) => {
      console.log('toggleAvailablity');
      try {
        if (!req.restaurantId) {
          throw new Error('Unauthenticated!');
        }
        const restaurant = await Restaurant.findById(req.restaurantId);
        restaurant.isAvailable = !restaurant.isAvailable;
        const result = await restaurant.save();
        return transformRestaurant(result);
      } catch (err) {
        throw err;
      }
    },
    updateCommission: async (_, args) => {
      console.log('updateCommission');
      try {
        const { id, commissionRate } = args;
        const result = await Restaurant.updateOne({ _id: id }, { commissionRate });
        if (result.modifiedCount > 0) {
          const restaurant = await Restaurant.findOne({ _id: id });
          return transformRestaurant(restaurant);
        } else {
          throw Error("Couldn't update the restaurant");
        }
      } catch (error) {
        console.log(error);
        throw error;
      }
    },
    orderPickedUp: async (_, args, { req }) => {
      logger.debug('Processing order pickup/delivery', { orderId: args._id });
      if (!req.restaurantId) {
        throw new Error('Unauthenticated!');
      }
      try {
        const order = await Order.findById(args._id);
        const status = order.isPickedUp ? ORDER_STATUS.COMPLETED : ORDER_STATUS.PICKED;
        order.orderStatus = status;
        const restaurant = await Restaurant.findById(req.restaurantId);
        if (!order.isPickedUp && status === ORDER_STATUS.PICKED) {
          order.completionTime = new Date(Date.now() + restaurant.deliveryTime * 60 * 1000);
        }

        order[order.isPickedUp ? 'deliveredAt' : 'pickedAt'] = new Date();

        const result = await order.save();
        const customer = await Customer.findOne({ customerId: result.customerId });
        const transformedOrder = await transformOrder(result);

        // 发送WhatsApp通知
        if (customer && customer.phone) {
          try {
            const sessions = await sessionService.findSessionsByCustomerPhone(customer.phone);
            if (sessions && sessions.length > 0) {
              // TODO: error handling
              // 查找与当前餐厅品牌匹配的会话
              let matchingSession = null;

              // 直接使用订单的品牌ID查找会话
              if (order.restaurantBrandId) {
                const brandId = order.restaurantBrandId.toString();
                matchingSession = sessions.find(session => session.brandRef && session.brandRef.id === brandId);

                logger.debug('Looking for session by brand', {
                  brandId,
                  found: !!matchingSession,
                  sessionsCount: sessions.length
                });
              }

              // 如果没找到匹配的会话，使用最近的会话
              if (!matchingSession) {
                logger.error('no session found for orderPickedUp');
              }

              if (matchingSession) {
                // 构建消息内容
                let message = '';

                if (order.isPickedUp) {
                  // 订单已送达
                  message =
                    `Your order #${order.orderId} has been delivered!\n` +
                    `We hope you enjoy your meal from ${restaurant.name}.\n` +
                    `Thank you for choosing us!`;
                } else {
                  // 订单已准备好待取货或配送
                  message = `Your order #${order.orderId} has been prepared`;

                  if (transformedOrder.isPickedUp) {
                    message += ` and is ready for pickup!\n`;
                  } else {
                    message += ` and is on its way!\n`;

                    // 只有配送订单才添加预计送达时间
                    message += `Estimated delivery time: ${new Date(order.completionTime).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true
                    })}.\n`;
                  }

                  message += `Thank you for your patience!`;
                }

                // 发送WhatsApp消息
                await whatsappService.sendDialogueText(matchingSession.dialogueId, message);

                logger.info('Order status notification sent', {
                  status: order.isPickedUp ? 'delivered' : 'picked',
                  customerPhone: customer.phone,
                  orderId: order.orderId,
                  dialogueId: matchingSession.dialogueId
                });
              }
            }
          } catch (error) {
            logger.error('Error sending WhatsApp notification', {
              error: error.message,
              stack: error.stack,
              customerPhone: customer.phone,
              orderId: order.orderId
            });
            // 不抛出错误，因为通知失败不应影响订单处理
          }
        }

        /*
        if (!transformedOrder.isPickedUp) {
          publishToUser(result.rider.toString(), transformedOrder, 'update')
        }
        publishToUser(result.user.toString(), transformedOrder, 'update')
        publishOrder(transformedOrder)
        sendNotificationToUser(result.user.toString(), transformedOrder)
        sendNotificationToCustomerWeb(
          user.notificationTokenWeb,
          `Order status: ${result.orderStatus}`,
          `Order ID ${result.orderId}`
        ) */
        return transformedOrder;
      } catch (err) {
        logger.error('Error in orderPickedUp', { error: err.message, stack: err.stack });
        throw err;
      }
    },
    updateDeliveryBoundsAndLocation: async (_, args) => {
      console.log('updateDeliveryBoundsAndLocation');
      const { id, bounds: newBounds, location: newLocation } = args;
      try {
        const restaurant = await Restaurant.findById(id);
        if (!restaurant) throw new Error('Restaurant does not exists');
        const location = new Point({
          type: 'Point',
          coordinates: [newLocation.longitude, newLocation.latitude]
        });
        console.log('Location: ', location);
        /*
        const zone = await Zone.findOne({
          location: { $geoIntersects: { $geometry: location } },
          isActive: true
        })
        console.log('Zone: ', zone)
        if (!zone) {
          return {
            success: false,
            message: "restaurant's location doesn't lie in any delivery zone"
          }
        }
          */
        const updated = await Restaurant.findByIdAndUpdate(
          id,
          {
            deliveryBounds: { type: 'Polygon', coordinates: newBounds },
            location
          },
          { new: true }
        );

        return {
          success: true,
          data: transformRestaurant(updated)
        };
      } catch (error) {
        console.log('updateDeliveryBoundsAndLocation', error);
        return {
          success: false,
          message: error.message
        };
      }
    }
  }
};
